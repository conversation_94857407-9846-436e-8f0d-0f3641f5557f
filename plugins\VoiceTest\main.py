import os
import asyncio
import time
import base64
import xml.etree.ElementTree as ET
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from typing import Optional, Dict, Any, Tuple, Set

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_voice_message, on_quote_message
from utils.plugin_base import PluginBase
from utils.temp_file_manager import create_temp_file, cleanup_file, mark_file_active, mark_file_inactive

class VoiceTest(PluginBase):
    description = "语音下载测试插件"
    author = "XYBot开发者"
    version = "1.0.0"
    plugin_name = "VoiceTest"

    def __init__(self):
        # 必须先调用父类初始化
        super().__init__()
        
        # 初始化临时目录
        self.temp_dir = Path("temp/voice_test")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 读取配置
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                config = {}
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {}

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["语音测试", "音频测试"])
        self.refer_command = config.get("refer_command", ["跟着唱", "语音复读"])
        self.command_format = config.get("command-format", "语音测试 - 回复一条语音消息来测试下载功能")
        
        # 初始化令牌桶限流
        rate_limit_config = config.get("rate_limit", {})
        self.tokens_per_second = rate_limit_config.get("tokens_per_second", 0.1)
        self.bucket_size = rate_limit_config.get("bucket_size", 3)
        self.tokens = self.bucket_size
        self.last_token_time = time.time()
        
        # 跟踪已激活语音测试的用户
        self.waiting_for_voice = set()

        # 语音消息缓存 - 存储语音消息的XML信息
        self.voice_xml_cache = {}
        self.cache_expiry = 1800  # 30分钟过期

    def _parse_voice_info(self, xml_content: str) -> Tuple[Optional[str], Optional[int]]:
        """
        从语音消息的XML内容中解析出voiceurl和length
        
        Args:
            xml_content: 语音消息的XML内容
            
        Returns:
            tuple: (voiceurl, length) 或在解析失败时返回 (None, None)
        """
        try:
            root = ET.fromstring(xml_content)
            voicemsg = root.find('voicemsg')
            if voicemsg is not None:
                voiceurl = voicemsg.get('voiceurl')
                length_str = voicemsg.get('length')
                # 确保将length转换为整数类型
                length = int(length_str) if length_str else None
                return voiceurl, length
            return None, None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 解析语音XML失败: {str(e)}")
            return None, None

    def _cache_voice_xml(self, msg_id: str, xml_content: str):
        """缓存语音消息的XML信息"""
        try:
            self.voice_xml_cache[msg_id] = {
                "xml": xml_content,
                "timestamp": time.time()
            }
            # 清理过期缓存
            self._clean_voice_cache()
        except Exception as e:
            logger.warning(f"[{self.plugin_name}] 缓存语音XML失败: {e}")

    def _clean_voice_cache(self):
        """清理过期的语音XML缓存"""
        try:
            now = time.time()
            expired_keys = []
            for key, data in self.voice_xml_cache.items():
                if now - data.get('timestamp', 0) > self.cache_expiry:
                    expired_keys.append(key)

            for key in expired_keys:
                self.voice_xml_cache.pop(key, None)

            if expired_keys:
                logger.debug(f"[{self.plugin_name}] 清理了 {len(expired_keys)} 个过期语音缓存")
        except Exception as e:
            logger.warning(f"[{self.plugin_name}] 清理语音缓存失败: {e}")

    def _get_cached_voice_xml(self, msg_id: str) -> Optional[str]:
        """从缓存中获取语音XML信息"""
        try:
            cache_data = self.voice_xml_cache.get(msg_id)
            if cache_data and time.time() - cache_data.get('timestamp', 0) < self.cache_expiry:
                return cache_data.get('xml')
            return None
        except Exception as e:
            logger.warning(f"[{self.plugin_name}] 获取缓存语音XML失败: {e}")
            return None

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return
            
        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        
        # 检查是否是插件命令
        command = content.split(" ", 1)
        if command[0] not in self.command:
            return
            
        # 将用户添加到等待语音的列表
        key = f"{wxid}:{user_wxid}"
        self.waiting_for_voice.add(key)
        
        # 发送简单提示
        await bot.send_at_message(
            wxid,
            "请发送一条语音消息进行测试",
            [user_wxid]
        )
    
    @on_voice_message
    async def handle_voice(self, bot: WechatAPIClient, message: dict):
        """处理语音消息"""
        if not self.enable:
            return

        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        msg_id = message.get("MsgId", "")

        # 缓存语音消息的XML信息，供引用功能使用
        xml_content = message.get("Content", "")
        if isinstance(xml_content, dict) and "string" in xml_content:
            xml_content = xml_content["string"]
        if xml_content and msg_id:
            self._cache_voice_xml(str(msg_id), xml_content)

        # 检查用户是否已激活语音测试
        key = f"{wxid}:{user_wxid}"
        if key not in self.waiting_for_voice:
            return
            
        try:
            # 从等待列表中移除用户
            self.waiting_for_voice.remove(key)
            
            # 检查令牌桶
            if not await self._acquire_token():
                await bot.send_at_message(
                    wxid,
                    "系统繁忙，请稍后再试",
                    [user_wxid]
                )
                return

            # 解析语音信息
            xml_content = message.get("Content", "")
            if isinstance(xml_content, dict) and "string" in xml_content:
                xml_content = xml_content["string"]
                
            voiceurl, length = self._parse_voice_info(xml_content)
            msg_id = message.get("MsgId", "")
            
            if not voiceurl or not length or not msg_id:
                await bot.send_at_message(
                    wxid,
                    "无法解析语音消息",
                    [user_wxid]
                )
                return
            
            # 下载语音
            silk_base64 = await bot.download_voice(msg_id, voiceurl, length)
            
            if silk_base64:
                # 保存原始数据但不提示 - 使用统一管理器
                silk_file = create_temp_file("voice_test", suffix=".silk", prefix=f"{msg_id}_")
                mark_file_active(silk_file)
                with open(silk_file, "w") as f:
                    f.write(silk_base64)
                mark_file_inactive(silk_file)
                # 延迟清理
                cleanup_file(silk_file, delay_seconds=300)  # 5分钟后清理
                
                try:
                    # 转换格式
                    wav_byte = await bot.silk_base64_to_wav_byte(silk_base64)
                    amr_base64 = bot.wav_byte_to_amr_base64(wav_byte)
                    
                    # 发送转换后的语音 - 成功时不显示提示
                    result = await bot.send_voice_message(
                        wxid,
                        amr_base64,
                        format="amr"
                    )
                    
                    # 只在失败时显示提示
                    if not (result and len(result) == 3 and result[2] != 0):
                        await bot.send_at_message(
                            wxid,
                            "语音发送失败",
                            [user_wxid]
                        )
                
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 语音处理失败: {str(e)}")
                    await bot.send_at_message(
                        wxid,
                        "语音处理失败",
                        [user_wxid]
                    )
                    
            else:
                await bot.send_at_message(
                    wxid,
                    "语音下载失败",
                    [user_wxid]
                )
                
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理语音消息异常: {str(e)}")
            await bot.send_at_message(
                wxid,
                "处理语音时出错",
                [user_wxid]
            )
    
    async def _acquire_token(self) -> bool:
        """尝试获取令牌桶中的令牌"""
        current_time = time.time()
        time_elapsed = current_time - self.last_token_time
        self.last_token_time = current_time
        
        # 添加新令牌
        self.tokens = min(self.bucket_size, self.tokens + time_elapsed * self.tokens_per_second)
        
        # 尝试获取令牌
        if self.tokens < 1:
            return False
            
        self.tokens -= 1
        return True

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是引用语音命令
        command = content.split(" ", 1)
        if command[0] not in self.refer_command:
            return

        # 获取引用信息
        quote_info = message.get("Quote", {})
        if not quote_info:
            await bot.send_at_message(
                wxid,
                "未找到引用信息",
                [user_wxid]
            )
            return

        # 检查引用的是否是语音消息 (MsgType 34 表示语音)
        quote_msg_type = quote_info.get("MsgType")
        if quote_msg_type != 34:
            await bot.send_at_message(
                wxid,
                "请引用一条语音消息",
                [user_wxid]
            )
            return

        try:
            # 检查令牌桶
            if not await self._acquire_token():
                await bot.send_at_message(
                    wxid,
                    "系统繁忙，请稍后再试",
                    [user_wxid]
                )
                return

            # 获取引用消息的ID
            quote_msg_id = quote_info.get("NewMsgId") or quote_info.get("Msgid")
            if not quote_msg_id:
                await bot.send_at_message(
                    wxid,
                    "无法获取引用消息ID",
                    [user_wxid]
                )
                return

            # 尝试从缓存中获取语音XML信息
            cached_xml = self._get_cached_voice_xml(str(quote_msg_id))
            if not cached_xml:
                await bot.send_at_message(
                    wxid,
                    "引用的语音消息已过期或未找到，请重新发送语音",
                    [user_wxid]
                )
                return

            # 从缓存的XML中解析语音信息
            voiceurl, length = self._parse_voice_info(cached_xml)
            if not voiceurl or not length:
                await bot.send_at_message(
                    wxid,
                    "无法解析引用的语音消息",
                    [user_wxid]
                )
                return

            # 下载语音
            silk_base64 = await bot.download_voice(str(quote_msg_id), voiceurl, length)

            if silk_base64:
                # 保存原始数据但不提示 - 使用统一管理器
                silk_file = create_temp_file("voice_test", suffix=".silk", prefix=f"{quote_msg_id}_")
                mark_file_active(silk_file)
                with open(silk_file, "w") as f:
                    f.write(silk_base64)
                mark_file_inactive(silk_file)
                # 延迟清理
                cleanup_file(silk_file, delay_seconds=300)  # 5分钟后清理

                try:
                    # 转换格式
                    wav_byte = await bot.silk_base64_to_wav_byte(silk_base64)
                    amr_base64 = bot.wav_byte_to_amr_base64(wav_byte)

                    # 发送转换后的语音 - 成功时不显示提示
                    result = await bot.send_voice_message(
                        wxid,
                        amr_base64,
                        format="amr"
                    )

                    # 只在失败时显示提示
                    if not (result and len(result) == 3 and result[2] != 0):
                        await bot.send_at_message(
                            wxid,
                            "语音发送失败",
                            [user_wxid]
                        )

                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 引用语音处理失败: {str(e)}")
                    await bot.send_at_message(
                        wxid,
                        "语音处理失败",
                        [user_wxid]
                    )

            else:
                await bot.send_at_message(
                    wxid,
                    "语音下载失败",
                    [user_wxid]
                )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理引用语音消息异常: {str(e)}")
            await bot.send_at_message(
                wxid,
                "处理引用语音时出错",
                [user_wxid]
            )